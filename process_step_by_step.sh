#!/bin/bash

# Postupné spracovanie WAV súborov - krok za krokom
echo "🏨 Postupné spracovanie Hotel zvukov"
echo "==================================="
echo ""

# Funkcia na spracovanie jedného súboru
process_file() {
    local input_file="$1"
    local base_name=$(basename "$input_file" .wav)
    
    echo "🔄 Spracovávam: $input_file"
    
    # Krok 1: Odstránenie ticha z konca
    echo "  🔇 Odstraňujem ticho z konca..."
    ffmpeg -i "$input_file" -af "silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB" "step1_${base_name}.wav" -y 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Chyba v kroku 1"
        return 1
    fi
    
    # Krok 2: Pridanie ticha na začiatok a koniec
    echo "  🔇 Pridávam ticho na zač<PERSON>tok a koniec..."
    ffmpeg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
           -i "step1_${base_name}.wav" \
           -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
           -filter_complex "[0:a][1:a][2:a]concat=n=3:v=0:a=1" \
           "step2_${base_name}.wav" -y 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Chyba v kroku 2"
        rm -f "step1_${base_name}.wav"
        return 1
    fi
    
    # Krok 3: Normalizácia hlasitosti
    echo "  🔊 Normalizujem hlasitosť na -16 LUFS..."
    ffmpeg -i "step2_${base_name}.wav" -af "loudnorm=I=-16:TP=-1.5:LRA=11" "final_${base_name}.wav" -y 2>/dev/null
    
    if [ $? -eq 0 ]; then
        # Nahradenie pôvodného súboru
        mv "final_${base_name}.wav" "$input_file"
        echo "  ✅ Úspešne spracované"
        
        # Vyčistenie dočasných súborov
        rm -f "step1_${base_name}.wav" "step2_${base_name}.wav"
        return 0
    else
        echo "  ❌ Chyba v kroku 3"
        rm -f "step1_${base_name}.wav" "step2_${base_name}.wav" "final_${base_name}.wav"
        return 1
    fi
}

# Spracovanie menších súborov najprv
small_files=(
    "OBJECT_GLASS_Break_Small_01.wav"
    "OldMetalClock6 44100 1.wav"
    "wooden door squeak open 1.wav"
)

echo "📊 Spracovávam menšie súbory najprv..."
echo ""

count=0
for file in "${small_files[@]}"; do
    if [ -f "$file" ]; then
        count=$((count + 1))
        echo "[$count/${#small_files[@]}] Processing: $file"
        process_file "$file"
        echo ""
    fi
done

echo "🎉 Spracovaných $count menších súborov."
echo ""
echo "💡 Pre veľké súbory (>50MB) odporúčam spracovanie jednotlivo kvôli času."

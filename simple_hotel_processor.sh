#!/bin/bash

# <PERSON><PERSON>duch<PERSON> skript na spracovanie Hotel WAV súborov
echo "🏨 Jednoduchý Hotel procesor"
echo "============================"
echo ""

# Vytvorenie backup priečinka
if [ ! -d "backup" ]; then
    mkdir backup
    echo "📁 Vytvoril som backup priečinok"
fi

# Vyčistenie dočasných súborov
rm -f temp_*.wav silence_*.wav no_silence.wav with_silence.wav processing_*.wav
echo "🧹 Vyčistené dočasné súbory"
echo ""

# Spracovanie konkrétnych súborov (menších najprv)
files_to_process=(
    "OBJECT_GLASS_Break_Small_01.wav"
    "wooden door squeak open 1.wav"
    "OldMetalClock6 44100 1.wav"
    "DoorKnock_DIGIB01-11.wav"
    "OBJKey_RATTLE-Small Metal Chain With Keys_B00M_CUCK.wav"
)

count=0
success=0

for file in "${files_to_process[@]}"; do
    if [ -f "$file" ]; then
        count=$((count + 1))
        echo "[$count] 🔄 Spracovávam: $file"
        
        # Backup
        if [ ! -f "backup/$file" ]; then
            cp "$file" "backup/$file"
            echo "  📁 Backup vytvorený"
        fi
        
        # Spracovanie v krokoch
        echo "  🔇 Odstraňujem ticho z konca..."
        ffmpeg -i "$file" -af "silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB" "temp_no_silence.wav" -y 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "  🔇 Pridávam ticho na začiatok a koniec..."
            ffmpeg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
                   -i "temp_no_silence.wav" \
                   -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
                   -filter_complex "[0:a][1:a][2:a]concat=n=3:v=0:a=1" \
                   "temp_with_silence.wav" -y 2>/dev/null
            
            if [ $? -eq 0 ]; then
                echo "  🔊 Normalizujem hlasitosť..."
                ffmpeg -i "temp_with_silence.wav" -af "loudnorm=I=-16:TP=-1.5:LRA=11" "temp_final.wav" -y 2>/dev/null
                
                if [ $? -eq 0 ]; then
                    mv "temp_final.wav" "$file"
                    echo "  ✅ Úspešne spracované"
                    success=$((success + 1))
                else
                    echo "  ❌ Chyba pri normalizácii"
                fi
            else
                echo "  ❌ Chyba pri pridávaní ticha"
            fi
        else
            echo "  ❌ Chyba pri odstraňovaní ticha"
        fi
        
        # Vyčistenie dočasných súborov
        rm -f temp_*.wav
        echo ""
    else
        echo "⚠️  Súbor neexistuje: $file"
    fi
done

echo "🎉 Spracovanie dokončené!"
echo "📊 Úspešne spracované: $success z $count súborov"
echo ""
echo "💡 Pre veľké súbory (>50MB) môžeš spustiť spracovanie jednotlivo."

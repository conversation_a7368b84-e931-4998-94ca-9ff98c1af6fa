#!/bin/bash

# Skript na normalizáciu hlasitosti MP3 súborov
# Ponúka dva režimy: Peak normalization a Loudness normalization

echo "🔊 Normalizácia hlasitosti MP3 súborov"
echo "======================================"
echo ""

# Funkcia na zobrazenie menu
show_menu() {
    echo "Vyber typ normalizácie:"
    echo "1) Peak Normalization (-1 dB) - nastaví najhlasnejší bod na -1dB"
    echo "2) Loudness Normalization (-23 LUFS) - štandardná vnímaná hlasitosť"
    echo "3) Loudness Normalization (-16 LUFS) - hlasnejšia vnímaná hlasitosť"
    echo "4) Analýza hlasitosti (bez zmien)"
    echo "5) Zrušiť"
    echo ""
}

# Funkcia na analýzu hlasitosti
analyze_volume() {
    echo "📊 Analýza hlasitosti súborov..."
    echo "==============================="
    echo ""
    
    for file in *.mp3; do
        if [ -f "$file" ]; then
            echo "🎵 $file:"
            ffmpeg -i "$file" -af "volumedetect" -f null - 2>&1 | grep -E "(mean_volume|max_volume)" | sed 's/.*] /  /'
            echo ""
        fi
    done
}

# Funkcia na peak normalizáciu
peak_normalize() {
    local target_db="$1"
    echo "🎯 Peak normalizácia na ${target_db}dB..."
    echo ""
    
    # Vytvorenie priečinka pre normalizované súbory
    if [ ! -d "normalized" ]; then
        mkdir normalized
        echo "📁 Vytvoril som priečinok 'normalized'"
    fi
    
    count=0
    total=$(ls *.mp3 2>/dev/null | wc -l)
    
    for file in *.mp3; do
        if [ -f "$file" ]; then
            count=$((count + 1))
            echo "[$count/$total] 🔄 Normalizujem: $file"
            
            # Peak normalizácia
            ffmpeg -i "$file" -af "volume=replaygain=track" -c:a libmp3lame -b:a 192k "normalized/$file" -y 2>/dev/null
            
            if [ $? -eq 0 ]; then
                echo "  ✅ Úspešne normalizované"
            else
                echo "  ❌ Chyba pri normalizácii"
            fi
        fi
    done
}

# Funkcia na loudness normalizáciu
loudness_normalize() {
    local target_lufs="$1"
    echo "🎯 Loudness normalizácia na ${target_lufs} LUFS..."
    echo ""
    
    # Vytvorenie priečinka pre normalizované súbory
    if [ ! -d "normalized" ]; then
        mkdir normalized
        echo "📁 Vytvoril som priečinok 'normalized'"
    fi
    
    count=0
    total=$(ls *.mp3 2>/dev/null | wc -l)
    
    for file in *.mp3; do
        if [ -f "$file" ]; then
            count=$((count + 1))
            echo "[$count/$total] 🔄 Normalizujem: $file"
            
            # Loudness normalizácia
            ffmpeg -i "$file" -af "loudnorm=I=${target_lufs}:TP=-1.5:LRA=11:print_format=summary" -c:a libmp3lame -b:a 192k "normalized/$file" -y 2>/dev/null
            
            if [ $? -eq 0 ]; then
                echo "  ✅ Úspešne normalizované"
            else
                echo "  ❌ Chyba pri normalizácii"
            fi
        fi
    done
}

# Hlavné menu
show_menu
read -p "Zadaj voľbu (1-5): " choice

case $choice in
    1)
        peak_normalize "-1"
        echo ""
        echo "🎉 Peak normalizácia dokončená!"
        echo "📁 Normalizované súbory sú v priečinku 'normalized'"
        ;;
    2)
        loudness_normalize "-23"
        echo ""
        echo "🎉 Loudness normalizácia (-23 LUFS) dokončená!"
        echo "📁 Normalizované súbory sú v priečinku 'normalized'"
        ;;
    3)
        loudness_normalize "-16"
        echo ""
        echo "🎉 Loudness normalizácia (-16 LUFS) dokončená!"
        echo "📁 Normalizované súbory sú v priečinku 'normalized'"
        ;;
    4)
        analyze_volume
        ;;
    5)
        echo "Zrušené."
        ;;
    *)
        echo "❌ Neplatná voľba."
        ;;
esac

echo ""
echo "💡 Tip: Porovnaj pôvodné a normalizované súbory pred nahradením originálov."

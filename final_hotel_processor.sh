#!/bin/bash

# Finálny skript na spracovanie všetkých WAV súborov v Hotel priečinku
echo "🏨 Finálne spracovanie Hotel zvukov"
echo "=================================="
echo ""

# Vytvorenie backup priečinka ak neexistuje
if [ ! -d "backup" ]; then
    mkdir backup
    echo "📁 Vytvoril som priečinok 'backup'"
fi

# Vyčistenie dočasných súborov
rm -f test_*.wav step*.wav final*.wav temp*.wav
echo "🧹 Vyčistené dočasné súbory"
echo ""

count=0
success=0
failed=0

echo "🔄 Začínam spracovanie všetkých WAV súborov..."
echo ""

# Spracovanie všetkých WAV súborov
find . -maxdepth 1 -name "*.wav" -type f -print0 | while IFS= read -r -d '' filepath; do
    # Odstránenie ./ z názvu súboru
    file="${filepath#./}"
    
    # Preskočenie ak je to backup súbor
    if [[ "$file" == backup/* ]]; then
        continue
    fi
    
    count=$((count + 1))
    echo "[$count] 🔄 Spracovávam: $file"
    
    # Vytvorenie backup kópie ak neexistuje
    if [ ! -f "backup/$file" ]; then
        cp "$file" "backup/$file"
        echo "  📁 Backup vytvorený"
    else
        echo "  📁 Backup už existuje"
    fi
    
    # Dočasný názov súboru
    temp_name="processing_${count}.wav"
    
    # Krok 1: Odstránenie ticha z konca + pridanie ticha na začiatok a koniec + normalizácia
    echo "  🔄 Kombinované spracovanie..."
    
    # Vytvorenie 1s ticha
    ffmpeg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 "silence_1s.wav" -y 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Chyba pri vytváraní ticha"
        continue
    fi
    
    # Odstránenie ticha z konca
    ffmpeg -i "$file" -af "silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB" "no_silence.wav" -y 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Chyba pri odstraňovaní ticha"
        rm -f "silence_1s.wav"
        continue
    fi
    
    # Pridanie ticha na začiatok a koniec
    ffmpeg -i "silence_1s.wav" -i "no_silence.wav" -i "silence_1s.wav" -filter_complex "[0:a][1:a][2:a]concat=n=3:v=0:a=1" "with_silence.wav" -y 2>/dev/null
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Chyba pri pridávaní ticha"
        rm -f "silence_1s.wav" "no_silence.wav"
        continue
    fi
    
    # Normalizácia hlasitosti
    ffmpeg -i "with_silence.wav" -af "loudnorm=I=-16:TP=-1.5:LRA=11" "$temp_name" -y 2>/dev/null
    
    if [ $? -eq 0 ]; then
        # Nahradenie pôvodného súboru
        mv "$temp_name" "$file"
        echo "  ✅ Úspešne spracované"
        success=$((success + 1))
    else
        echo "  ❌ Chyba pri normalizácii"
        failed=$((failed + 1))
        rm -f "$temp_name"
    fi
    
    # Vyčistenie dočasných súborov
    rm -f "silence_1s.wav" "no_silence.wav" "with_silence.wav"
    echo ""
done

echo "🎉 Spracovanie dokončené!"
echo "📊 Štatistiky:"
echo "  ✅ Úspešne spracované: $success súborov"
echo "  ❌ Neúspešné: $failed súborov"
echo ""
echo "📁 Výsledky:"
echo "  • Odstránené dlhé ticho z konca"
echo "  • Pridaná 1 sekunda ticha na začiatok a koniec"
echo "  • Normalizovaná hlasitosť na -16 LUFS"
echo "  • Záložné kópie v priečinku 'backup'"

#!/bin/bash

# Univerzálny skript na spracovanie všetkých WAV súborov
echo "🏨 Univerzálny Hotel procesor"
echo "============================="
echo ""

# Vytvorenie backup priečinka
if [ ! -d "backup" ]; then
    mkdir backup
    echo "📁 Vytvoril som backup priečinok"
fi

# Vyčistenie dočasných súborov
rm -f test_*.wav temp_*.wav
echo "🧹 Vyčistené dočasné súbory"
echo ""

count=0
success=0
failed=0

echo "🔍 Hľadám WAV súbory..."

# Použitie for cyklu s glob pattern
for file in *.wav; do
    # <PERSON><PERSON><PERSON><PERSON>, či súbor skutočne existuje (pre prípad, že glob nevráti nič)
    if [ -f "$file" ]; then
        count=$((count + 1))
        echo ""
        echo "[$count] 🔄 Spracovávam: $file"
        
        # Vytvorenie backup kópie
        if [ ! -f "backup/$file" ]; then
            cp "$file" "backup/$file" 2>/dev/null
            if [ $? -eq 0 ]; then
                echo "  📁 Backup vytvorený"
            else
                echo "  ⚠️  Problém s vytvorením backup (možno kvôli názvu súboru)"
            fi
        else
            echo "  📁 Backup už existuje"
        fi
        
        # Dočasný názov súboru (jednoduchý)
        temp_file="temp_${count}.wav"
        
        echo "  🔇 Odstraňujem ticho z konca..."
        ffmpeg -i "$file" -af "silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB" "$temp_file" -y 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "  🔇 Pridávam ticho na začiatok a koniec..."
            ffmpeg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
                   -i "$temp_file" \
                   -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
                   -filter_complex "[0:a][1:a][2:a]concat=n=3:v=0:a=1" \
                   "temp2_${count}.wav" -y 2>/dev/null
            
            if [ $? -eq 0 ]; then
                echo "  🔊 Normalizujem hlasitosť na -16 LUFS..."
                ffmpeg -i "temp2_${count}.wav" -af "loudnorm=I=-16:TP=-1.5:LRA=11" "final_${count}.wav" -y 2>/dev/null
                
                if [ $? -eq 0 ]; then
                    # Nahradenie pôvodného súboru
                    mv "final_${count}.wav" "$file"
                    echo "  ✅ Úspešne spracované"
                    success=$((success + 1))
                else
                    echo "  ❌ Chyba pri normalizácii"
                    failed=$((failed + 1))
                fi
            else
                echo "  ❌ Chyba pri pridávaní ticha"
                failed=$((failed + 1))
            fi
        else
            echo "  ❌ Chyba pri odstraňovaní ticha"
            failed=$((failed + 1))
        fi
        
        # Vyčistenie dočasných súborov
        rm -f "$temp_file" "temp2_${count}.wav" "final_${count}.wav"
        
    fi
done

echo ""
echo "🎉 Spracovanie dokončené!"
echo "📊 Štatistiky:"
echo "  📁 Celkom súborov: $count"
echo "  ✅ Úspešne spracované: $success"
echo "  ❌ Neúspešné: $failed"
echo ""
echo "📁 Výsledky:"
echo "  • Odstránené dlhé ticho z konca"
echo "  • Pridaná 1 sekunda ticha na začiatok a koniec"
echo "  • Normalizovaná hlasitosť na -16 LUFS"
echo "  • Záložné kópie v priečinku 'backup'"

#!/bin/bash

# Jednoduchý skript na spracovanie WAV súborov
echo "🏨 Spracovanie Hotel zvukov - jednoduchá verzia"
echo "=============================================="
echo ""

count=0

# Spracovanie konkrétnych súborov (bez dočasných súborov)
for file in "DoorKnock_DIGIB01-11.wav" "OBJECT_GLASS_Break_Small_01.wav" "OldMetalClock6 44100 1.wav" "wooden door squeak open 1.wav" "OBJKey_RATTLE-Small Metal Chain With Keys_B00M_CUCK.wav"; do
    if [ -f "$file" ]; then
        count=$((count + 1))
        echo "[$count] 🔄 Spracovávam: $file"
        
        # Kombinované spracovanie v jednom príkaze
        ffmpeg -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
               -i "$file" \
               -f lavfi -i anullsrc=channel_layout=stereo:sample_rate=44100 -t 1 \
               -filter_complex "
                   [0:a][1:a]concat=n=2:v=0:a=1[with_start];
                   [with_start]silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB[no_end_silence];
                   [no_end_silence][2:a]concat=n=2:v=0:a=1[with_end];
                   [with_end]loudnorm=I=-16:TP=-1.5:LRA=11[normalized]
               " \
               -map "[normalized]" "processed_$file" -y 2>/dev/null
        
        if [ $? -eq 0 ]; then
            mv "processed_$file" "$file"
            echo "  ✅ Úspešne spracované"
        else
            echo "  ❌ Chyba pri spracovaní"
            rm -f "processed_$file"
        fi
        echo ""
    fi
done

echo "🎉 Spracovaných $count menších súborov."
echo "💡 Veľké súbory spracujem osobne kvôli ich veľkosti."

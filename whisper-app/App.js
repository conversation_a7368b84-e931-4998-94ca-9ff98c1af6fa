import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Alert,
  Dimensions,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import MainCircle from './src/components/MainCircle';
import useAudioRecorder from './src/components/AudioRecorder';
import useAudioPlayer from './src/components/AudioPlayer';
import WhisperCard from './src/components/WhisperCard';
import FloatingMenu from './src/components/FloatingMenu';
import useWhispers from './src/hooks/useWhispers';
import { colors, fonts, spacing } from './src/styles/theme';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [currentMode, setCurrentMode] = useState('listen');
  const [currentWhisper, setCurrentWhisper] = useState(null);
  const [browseWhispers, setBrowseWhispers] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  const {
    userId,
    isLoading,
    error,
    uploadWhisper,
    getRandomWhisperForUser,
    getBrowseWhispers,
    handleLikeWhisper,
    handleReportWhisper,
    clearError,
  } = useWhispers();

  const audioRecorder = useAudioRecorder({
    onRecordingStart: () => setIsRecording(true),
    onRecordingStop: () => setIsRecording(false),
    onRecordingComplete: handleRecordingComplete,
  });

  const audioPlayer = useAudioPlayer({
    audioUrl: currentWhisper?.audioUrl,
    onPlaybackStart: () => setIsPlaying(true),
    onPlaybackEnd: () => setIsPlaying(false),
  });

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [{ text: 'OK', onPress: clearError }]);
    }
  }, [error]);

  useEffect(() => {
    if (currentMode === 'listen') {
      loadRandomWhisper();
    } else if (currentMode === 'browse') {
      loadBrowseWhispers();
    }
  }, [currentMode]);

  const handleRecordingComplete = async (audioUri) => {
    if (!audioUri) return;

    try {
      const whisperId = await uploadWhisper(audioUri);
      if (whisperId) {
        Alert.alert('Success', 'Your whisper has been shared! 👻');
        setCurrentMode('listen');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to upload whisper');
    }
  };

  const loadRandomWhisper = async () => {
    console.log('Loading random whisper...');
    try {
      const whisper = await getRandomWhisperForUser();
      console.log('Random whisper loaded:', whisper);
      setCurrentWhisper(whisper);

      if (!whisper) {
        Alert.alert('No Whispers', 'No whispers available yet. Try recording one first! 🎙️');
      }
    } catch (error) {
      console.error('Error loading random whisper:', error);
      Alert.alert('Error', 'Failed to load whisper. Please try again.');
    }
  };

  const loadBrowseWhispers = async () => {
    const whispers = await getBrowseWhispers();
    setBrowseWhispers(whispers);
  };

  const handleMainCirclePress = () => {
    switch (currentMode) {
      case 'record':
        if (isRecording) {
          audioRecorder.stopRecording();
        } else {
          audioRecorder.startRecording();
        }
        break;
      case 'listen':
        if (currentWhisper) {
          audioPlayer.playAudio();
        } else {
          loadRandomWhisper();
        }
        break;
      case 'browse':
        loadBrowseWhispers();
        break;
    }
  };

  const renderContent = () => {
    switch (currentMode) {
      case 'record':
        return (
          <View style={styles.recordContent}>
            {isRecording && (
              <Text style={styles.recordingText}>
                Recording: {audioRecorder.formatDuration(audioRecorder.recordingDuration)}
              </Text>
            )}
            <Text style={styles.hintText}>
              {isRecording ? 'Release to stop' : 'Hold the circle to record your whisper'}
            </Text>
          </View>
        );

      case 'listen':
        return (
          <View style={styles.listenContent}>
            {currentWhisper ? (
              <>
                {isPlaying && (
                  <View style={styles.progressContainer}>
                    <View style={styles.progressBar}>
                      <View
                        style={[
                          styles.progressFill,
                          { width: `${audioPlayer.getProgress() * 100}%` },
                        ]}
                      />
                    </View>
                    <Text style={styles.timeText}>
                      {audioPlayer.formatTime(audioPlayer.position)} / {audioPlayer.formatTime(audioPlayer.duration)}
                    </Text>
                  </View>
                )}
                <Text style={styles.hintText}>
                  {isPlaying ? 'Playing anonymous whisper...' : 'Tap to play a random whisper'}
                </Text>
              </>
            ) : (
              <Text style={styles.hintText}>
                {isLoading ? 'Finding a whisper...' : 'No whispers available. Tap to try again.'}
              </Text>
            )}
          </View>
        );

      case 'browse':
        return (
          <ScrollView style={styles.browseContent} showsVerticalScrollIndicator={false}>
            {browseWhispers.length > 0 ? (
              browseWhispers.map((whisper) => (
                <WhisperCard
                  key={whisper.id}
                  whisper={whisper}
                  onLike={handleLikeWhisper}
                  onReport={handleReportWhisper}
                />
              ))
            ) : (
              <View style={styles.emptyState}>
                <Text style={styles.emptyText}>
                  {isLoading ? 'Loading whispers...' : 'No whispers found'}
                </Text>
              </View>
            )}
          </ScrollView>
        );

      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ExpoStatusBar style="light" backgroundColor={colors.black} />

      <FloatingMenu
        currentMode={currentMode}
        onModeChange={setCurrentMode}
      />

      <View style={styles.mainContent}>
        {currentMode !== 'browse' && (
          <View style={styles.circleContainer}>
            <MainCircle
              mode={currentMode}
              onPress={handleMainCirclePress}
              isActive={isRecording || isPlaying}
              isRecording={isRecording}
              isPlaying={isPlaying}
            />
          </View>
        )}

        <View style={styles.contentContainer}>
          {renderContent()}
        </View>
      </View>

      {/* App Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.appTitle}>WHISPER</Text>
        <Text style={styles.appSubtitle}>👻 Anonymous Audio Sharing</Text>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.black,
  },
  mainContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    width: '100%',
    paddingHorizontal: spacing.lg,
  },
  recordContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  listenContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  browseContent: {
    flex: 1,
    paddingTop: spacing.xl,
  },
  recordingText: {
    color: colors.neonPurple,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    marginBottom: spacing.md,
  },
  hintText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
    opacity: 0.8,
  },
  progressContainer: {
    width: '80%',
    marginBottom: spacing.lg,
  },
  progressBar: {
    height: 4,
    backgroundColor: colors.darkGray,
    borderRadius: 2,
    marginBottom: spacing.sm,
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.neonPurple,
    borderRadius: 2,
  },
  timeText: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    textAlign: 'center',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xxl,
  },
  emptyText: {
    color: colors.lightGray,
    fontSize: fonts.size.medium,
    textAlign: 'center',
  },
  titleContainer: {
    position: 'absolute',
    bottom: 60,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  appTitle: {
    color: colors.neonPurple,
    fontSize: fonts.size.xxlarge,
    fontWeight: 'bold',
    letterSpacing: 4,
  },
  appSubtitle: {
    color: colors.lightGray,
    fontSize: fonts.size.small,
    marginTop: spacing.xs,
    opacity: 0.7,
  },
});

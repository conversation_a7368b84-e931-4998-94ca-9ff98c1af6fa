import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  query,
  orderBy,
  limit,
  updateDoc,
  doc,
  where,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from './firebase';

// Upload audio file to Firebase Storage
export const uploadAudio = async (audioUri, userId) => {
  try {
    const response = await fetch(audioUri);
    const blob = await response.blob();
    
    const fileName = `whispers/${userId}_${Date.now()}.m4a`;
    const storageRef = ref(storage, fileName);
    
    const snapshot = await uploadBytes(storageRef, blob);
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return downloadURL;
  } catch (error) {
    console.error('Error uploading audio:', error);
    throw error;
  }
};

// Create new whisper in Firestore
export const createWhisper = async (audioUrl, userId) => {
  try {
    const whisperData = {
      audioUrl,
      userId,
      timestamp: serverTimestamp(),
      likes: 0,
      reports: 0,
      isActive: true
    };
    
    const docRef = await addDoc(collection(db, 'whispers'), whisperData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating whisper:', error);
    throw error;
  }
};

// Get random whisper
export const getRandomWhisper = async (excludeUserId = null) => {
  try {
    let q = query(
      collection(db, 'whispers'),
      where('isActive', '==', true),
      orderBy('timestamp', 'desc'),
      limit(50)
    );
    
    const querySnapshot = await getDocs(q);
    const whispers = [];
    
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      if (!excludeUserId || data.userId !== excludeUserId) {
        whispers.push({ id: doc.id, ...data });
      }
    });
    
    if (whispers.length === 0) return null;
    
    const randomIndex = Math.floor(Math.random() * whispers.length);
    return whispers[randomIndex];
  } catch (error) {
    console.error('Error getting random whisper:', error);
    throw error;
  }
};

// Get recent whispers for browse mode
export const getRecentWhispers = async (limitCount = 20) => {
  try {
    const q = query(
      collection(db, 'whispers'),
      where('isActive', '==', true),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const whispers = [];
    
    querySnapshot.forEach((doc) => {
      whispers.push({ id: doc.id, ...doc.data() });
    });
    
    return whispers;
  } catch (error) {
    console.error('Error getting recent whispers:', error);
    throw error;
  }
};

// Like a whisper
export const likeWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);
    await updateDoc(whisperRef, {
      likes: increment(1)
    });
  } catch (error) {
    console.error('Error liking whisper:', error);
    throw error;
  }
};

// Report a whisper
export const reportWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);

    // Get current document to check report count
    const whisperDoc = await getDoc(whisperRef);
    if (whisperDoc.exists()) {
      const currentReports = whisperDoc.data().reports || 0;
      const newReports = currentReports + 1;

      await updateDoc(whisperRef, {
        reports: newReports,
        isActive: newReports < 3 // Deactivate if 3+ reports
      });
    }
  } catch (error) {
    console.error('Error reporting whisper:', error);
    throw error;
  }
};

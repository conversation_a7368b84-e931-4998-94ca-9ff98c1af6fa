import {
  collection,
  addDoc,
  getDocs,
  getDoc,
  query,
  orderBy,
  limit,
  updateDoc,
  doc,
  where,
  serverTimestamp,
  increment
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { db, storage } from './firebase';

// Upload audio file to Firebase Storage
export const uploadAudio = async (audioUri, userId) => {
  try {
    console.log('Uploading audio:', { audioUri, userId });

    const response = await fetch(audioUri);
    const blob = await response.blob();
    console.log('Audio blob size:', blob.size);

    const fileName = `whispers/${userId}_${Date.now()}.m4a`;
    const storageRef = ref(storage, fileName);
    console.log('Storage path:', fileName);

    const snapshot = await uploadBytes(storageRef, blob);
    console.log('Upload completed, getting download URL...');

    const downloadURL = await getDownloadURL(snapshot.ref);
    console.log('Download URL:', downloadURL);

    return downloadURL;
  } catch (error) {
    console.error('Error uploading audio:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Create new whisper in Firestore
export const createWhisper = async (audioUrl, userId) => {
  try {
    console.log('Creating whisper with data:', { audioUrl, userId });

    const whisperData = {
      audioUrl,
      userId,
      timestamp: serverTimestamp(),
      likes: 0,
      reports: 0,
      isActive: true
    };

    console.log('Whisper data to save:', whisperData);

    const docRef = await addDoc(collection(db, 'whispers'), whisperData);
    console.log('Whisper created with ID:', docRef.id);

    return docRef.id;
  } catch (error) {
    console.error('Error creating whisper:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Get random whisper
export const getRandomWhisper = async (excludeUserId = null) => {
  try {
    console.log('Fetching whispers from Firebase...');

    // Simplified query - just get all whispers first
    const q = query(
      collection(db, 'whispers'),
      limit(50)
    );

    const querySnapshot = await getDocs(q);
    const whispers = [];

    console.log('Query snapshot size:', querySnapshot.size);

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      console.log('Whisper data:', { id: doc.id, ...data });

      // Filter active whispers and exclude user's own whispers
      if (data.isActive !== false && (!excludeUserId || data.userId !== excludeUserId)) {
        whispers.push({ id: doc.id, ...data });
      }
    });

    console.log('Filtered whispers count:', whispers.length);

    if (whispers.length === 0) return null;

    const randomIndex = Math.floor(Math.random() * whispers.length);
    const selectedWhisper = whispers[randomIndex];
    console.log('Selected whisper:', selectedWhisper);

    return selectedWhisper;
  } catch (error) {
    console.error('Error getting random whisper:', error);
    console.error('Error details:', error.message);
    throw error;
  }
};

// Get recent whispers for browse mode
export const getRecentWhispers = async (limitCount = 20) => {
  try {
    const q = query(
      collection(db, 'whispers'),
      where('isActive', '==', true),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    
    const querySnapshot = await getDocs(q);
    const whispers = [];
    
    querySnapshot.forEach((doc) => {
      whispers.push({ id: doc.id, ...doc.data() });
    });
    
    return whispers;
  } catch (error) {
    console.error('Error getting recent whispers:', error);
    throw error;
  }
};

// Like a whisper
export const likeWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);
    await updateDoc(whisperRef, {
      likes: increment(1)
    });
  } catch (error) {
    console.error('Error liking whisper:', error);
    throw error;
  }
};

// Report a whisper
export const reportWhisper = async (whisperId) => {
  try {
    const whisperRef = doc(db, 'whispers', whisperId);

    // Get current document to check report count
    const whisperDoc = await getDoc(whisperRef);
    if (whisperDoc.exists()) {
      const currentReports = whisperDoc.data().reports || 0;
      const newReports = currentReports + 1;

      await updateDoc(whisperRef, {
        reports: newReports,
        isActive: newReports < 3 // Deactivate if 3+ reports
      });
    }
  } catch (error) {
    console.error('Error reporting whisper:', error);
    throw error;
  }
};

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { colors, fonts, shadows } from '../styles/theme';

const { width, height } = Dimensions.get('window');
const CIRCLE_SIZE = 200;

const MainCircle = ({ mode, onPress, isActive, isRecording, isPlaying }) => {
  const [scaleAnim] = useState(new Animated.Value(1));
  const [glowAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (isActive || isRecording || isPlaying) {
      // Pulse animation - use same useNativeDriver setting for both
      Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: false,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: false,
          }),
        ])
      ).start();

      // Glow animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(glowAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: false,
          }),
          Animated.timing(glowAnim, {
            toValue: 0,
            duration: 1500,
            useNativeDriver: false,
          }),
        ])
      ).start();
    } else {
      scaleAnim.setValue(1);
      glowAnim.setValue(0);
    }
  }, [isActive, isRecording, isPlaying]);

  const getCircleStyle = () => {
    let backgroundColor = colors.deepPurple;
    let borderColor = colors.neonGreen;
    
    if (mode === 'record' && isRecording) {
      backgroundColor = colors.neonGreen;
      borderColor = colors.white;
    } else if (mode === 'listen' && isPlaying) {
      backgroundColor = colors.deepPurple;
      borderColor = colors.neonGreen;
    }

    return {
      backgroundColor,
      borderColor,
    };
  };

  const getModeText = () => {
    switch (mode) {
      case 'record':
        return isRecording ? 'Recording...' : 'Hold to Record';
      case 'listen':
        return isPlaying ? 'Playing...' : 'Tap to Listen';
      case 'browse':
        return 'Browse Whispers';
      default:
        return 'Whisper';
    }
  };

  const getModeIcon = () => {
    switch (mode) {
      case 'record':
        return '🎙️';
      case 'listen':
        return '👂';
      case 'browse':
        return '📚';
      default:
        return '👻';
    }
  };

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.2, 0.8],
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.glowCircle,
          {
            opacity: glowOpacity,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      />
      <Animated.View
        style={[
          styles.circle,
          getCircleStyle(),
          {
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <TouchableOpacity
          style={styles.touchable}
          onPress={onPress}
          activeOpacity={0.8}
        >
          <Text style={styles.icon}>{getModeIcon()}</Text>
          <Text style={styles.modeText}>{getModeText()}</Text>
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  glowCircle: {
    position: 'absolute',
    width: CIRCLE_SIZE + 40,
    height: CIRCLE_SIZE + 40,
    borderRadius: (CIRCLE_SIZE + 40) / 2,
    backgroundColor: colors.neonGreen,
    opacity: 0.3,
  },
  circle: {
    width: CIRCLE_SIZE,
    height: CIRCLE_SIZE,
    borderRadius: CIRCLE_SIZE / 2,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.glow,
  },
  touchable: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    fontSize: 40,
    marginBottom: 8,
  },
  modeText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    textAlign: 'center',
    opacity: 0.9,
  },
});

export default MainCircle;

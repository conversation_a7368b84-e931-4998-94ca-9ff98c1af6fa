import { useState, useRef } from 'react';
import { Alert } from 'react-native';
import { Audio } from 'expo-av';
import * as MediaLibrary from 'expo-media-library';

const useAudioRecorder = ({ onRecordingComplete, onRecordingStart, onRecordingStop }) => {
  const [recording, setRecording] = useState(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const recordingRef = useRef(null);
  const durationInterval = useRef(null);

  const startRecording = async () => {
    try {
      // Request permissions
      const { status: audioStatus } = await Audio.requestPermissionsAsync();

      if (audioStatus !== 'granted') {
        Alert.alert('Permission required', 'Please grant audio recording permissions');
        return;
      }

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Start recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);
      recordingRef.current = newRecording;

      // Start duration counter
      durationInterval.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          // Auto-stop at 30 seconds
          if (newDuration >= 30) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

      onRecordingStart && onRecordingStart();
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const stopRecording = async () => {
    try {
      if (!recording) return;

      setIsRecording(false);
      clearInterval(durationInterval.current);

      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      setRecording(null);
      recordingRef.current = null;

      onRecordingStop && onRecordingStop();
      onRecordingComplete && onRecordingComplete(uri);
    } catch (error) {
      console.error('Failed to stop recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    startRecording,
    stopRecording,
    isRecording,
    recordingDuration,
    formatDuration,
  };
};

export default useAudioRecorder;

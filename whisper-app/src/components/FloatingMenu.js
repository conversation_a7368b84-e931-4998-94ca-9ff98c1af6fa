import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Animated,
  Dimensions,
} from 'react-native';
import { colors, fonts, spacing, borderRadius, shadows } from '../styles/theme';

const { width, height } = Dimensions.get('window');

const FloatingMenu = ({ onModeChange, currentMode }) => {
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [fadeAnim] = useState(new Animated.Value(0));

  const modes = [
    { key: 'record', icon: '🎙️', label: 'Record' },
    { key: 'listen', icon: '👂', label: 'Listen' },
    { key: 'browse', icon: '📚', label: 'Browse' },
  ];

  const openModeSelector = () => {
    setShowModeSelector(true);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeModeSelector = () => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setShowModeSelector(false);
    });
  };

  const selectMode = (mode) => {
    onModeChange(mode);
    closeModeSelector();
  };

  return (
    <>
      {/* Floating Buttons */}
      <View style={styles.floatingContainer}>
        {/* Mode Selector Button */}
        <TouchableOpacity
          style={styles.floatingButton}
          onPress={openModeSelector}
        >
          <Text style={styles.floatingIcon}>🎭</Text>
        </TouchableOpacity>

        {/* Settings Button */}
        <TouchableOpacity
          style={[styles.floatingButton, styles.settingsButton]}
          onPress={() => {
            // TODO: Implement settings
            console.log('Settings pressed');
          }}
        >
          <Text style={styles.floatingIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      {/* Mode Selector Modal */}
      <Modal
        visible={showModeSelector}
        transparent={true}
        animationType="none"
        onRequestClose={closeModeSelector}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeModeSelector}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: fadeAnim,
                transform: [
                  {
                    scale: fadeAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.8, 1],
                    }),
                  },
                ],
              },
            ]}
          >
            <Text style={styles.modalTitle}>Choose Mode</Text>
            
            {modes.map((mode) => (
              <TouchableOpacity
                key={mode.key}
                style={[
                  styles.modeOption,
                  currentMode === mode.key && styles.modeOptionActive,
                ]}
                onPress={() => selectMode(mode.key)}
              >
                <Text style={styles.modeIcon}>{mode.icon}</Text>
                <Text style={styles.modeLabel}>{mode.label}</Text>
                {currentMode === mode.key && (
                  <Text style={styles.activeIndicator}>✓</Text>
                )}
              </TouchableOpacity>
            ))}
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  floatingContainer: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    zIndex: 1000,
  },
  floatingButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.deepPurple,
    borderWidth: 2,
    borderColor: colors.neonPurple,
    justifyContent: 'center',
    alignItems: 'center',
    ...shadows.glow,
  },
  settingsButton: {
    backgroundColor: colors.darkGray,
    borderColor: colors.lightGray,
  },
  floatingIcon: {
    fontSize: 24,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.deepPurple,
    borderRadius: borderRadius.lg,
    borderWidth: 2,
    borderColor: colors.neonPurple,
    padding: spacing.xl,
    width: width * 0.8,
    maxWidth: 300,
    ...shadows.glow,
  },
  modalTitle: {
    color: colors.white,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  modeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.md,
    marginBottom: spacing.sm,
    backgroundColor: colors.darkGray,
  },
  modeOptionActive: {
    backgroundColor: colors.neonPurple,
  },
  modeIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  modeLabel: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: 'bold',
    flex: 1,
  },
  activeIndicator: {
    color: colors.white,
    fontSize: fonts.size.large,
    fontWeight: 'bold',
  },
});

export default FloatingMenu;

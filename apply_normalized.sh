#!/bin/bash

# Skript na nahradenie pôvodných súborov normalizovanými verziami

echo "🔄 Nahradenie pôvodných súborov normalizovanými verziami"
echo "======================================================"
echo ""

# Kontrola, či existuje prieč<PERSON>k normalized
if [ ! -d "normalized" ]; then
    echo "❌ Priečinok 'normalized' neexistuje!"
    echo "Najprv spusti normalizáciu pomocou normalize_volume.sh"
    exit 1
fi

# Kontrola, či existuje priečinok backup
if [ ! -d "backup" ]; then
    echo "❌ Priečinok 'backup' neexistuje!"
    echo "Záložné kópie nie sú k dispozícii. Pokračovanie môže byť nebezpečné."
    read -p "Ch<PERSON><PERSON> pok<PERSON>? (y/N): " confirm
    if [[ ! $confirm =~ ^[Yy]$ ]]; then
        echo "Zrušené."
        exit 1
    fi
fi

echo "📋 Súbory na nahradenie:"
count=0
for file in normalized/*.mp3; do
    if [ -f "$file" ]; then
        basename_file=$(basename "$file")
        if [ -f "$basename_file" ]; then
            echo "  ✓ $basename_file"
            count=$((count + 1))
        fi
    fi
done

echo ""
echo "📊 Celkom súborov na nahradenie: $count"
echo ""

if [ $count -eq 0 ]; then
    echo "❌ Žiadne súbory na nahradenie!"
    exit 1
fi

echo "⚠️  UPOZORNENIE: Táto operácia nahradí pôvodné súbory!"
echo "📁 Záložné kópie sú v priečinku 'backup'"
echo ""
read -p "Pokračovať s nahradením? (y/N): " confirm

if [[ ! $confirm =~ ^[Yy]$ ]]; then
    echo "Zrušené."
    exit 0
fi

echo ""
echo "🔄 Nahrádzam súbory..."
echo ""

replaced=0
for file in normalized/*.mp3; do
    if [ -f "$file" ]; then
        basename_file=$(basename "$file")
        if [ -f "$basename_file" ]; then
            echo "[$((replaced + 1))/$count] 🔄 Nahrádzam: $basename_file"
            
            # Nahradenie súboru
            cp "$file" "$basename_file"
            
            if [ $? -eq 0 ]; then
                echo "  ✅ Úspešne nahradené"
                replaced=$((replaced + 1))
            else
                echo "  ❌ Chyba pri nahrádzaní"
            fi
        fi
    fi
done

echo ""
echo "🎉 Hotovo! Nahradených $replaced súborov."
echo ""
echo "📁 Štruktúra priečinkov:"
echo "  • Aktuálne súbory: normalizované na -16 LUFS"
echo "  • backup/: pôvodné súbory (pred odstránením ticha)"
echo "  • normalized/: normalizované verzie (môžeš zmazať)"
echo ""
echo "💡 Tip: Môžeš teraz zmazať priečinok 'normalized' príkazom: rm -rf normalized"

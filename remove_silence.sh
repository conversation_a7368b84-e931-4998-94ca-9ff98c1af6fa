#!/bin/bash

# Skript na odstránenie ticha z konca MP3 súborov
# Vyt<PERSON><PERSON> záložné kópie pôvodných súborov

echo "🎵 Začínam odstraňovanie ticha z MP3 súborov..."
echo ""

# Vytvorenie priečinka pre zá<PERSON><PERSON>n<PERSON> kópie
if [ ! -d "backup" ]; then
    mkdir backup
    echo "📁 Vytvoril som priečinok 'backup' pre z<PERSON><PERSON><PERSON><PERSON><PERSON> kópie"
fi

# Počítadlo spracovaných súborov
count=0
total=$(ls *.mp3 2>/dev/null | wc -l)

echo "📊 Našiel som $total MP3 súborov na spracovanie"
echo ""

# Spracovanie každého MP3 súboru
for file in *.mp3; do
    if [ -f "$file" ]; then
        count=$((count + 1))
        echo "[$count/$total] 🔄 Spracovávam: $file"
        
        # Vytvorenie záložnej kópie
        cp "$file" "backup/$file"
        echo "  ✅ Záložná kópia vytvorená"
        
        # Dočasný názov súboru
        temp_file="temp_${file}"
        
        # Odstránenie ticha z konca súboru
        # -af silenceremove=stop_periods=-1:stop_duration=1:stop_threshold=-50dB
        # stop_periods=-1: odstráni všetky tiché časti z konca
        # stop_duration=1: minimálna dĺžka ticha na odstránenie (1 sekunda)
        # stop_threshold=-50dB: prah hlasitosti (čo sa považuje za ticho)
        
        ffmpeg -i "$file" -af "silenceremove=stop_periods=-1:stop_duration=0.5:stop_threshold=-40dB" -c:a libmp3lame -b:a 192k "$temp_file" -y 2>/dev/null
        
        if [ $? -eq 0 ]; then
            # Ak bolo spracovanie úspešné, nahradíme pôvodný súbor
            mv "$temp_file" "$file"
            echo "  ✅ Ticho odstránené úspešne"
        else
            # Ak sa vyskytla chyba, odstránime dočasný súbor
            rm -f "$temp_file"
            echo "  ❌ Chyba pri spracovaní súboru"
        fi
        
        echo ""
    fi
done

echo "🎉 Hotovo! Spracovaných $count súborov."
echo "📁 Záložné kópie sú uložené v priečinku 'backup'"
echo ""
echo "💡 Tip: Ak nie si spokojný s výsledkom, môžeš obnoviť pôvodné súbory z priečinka 'backup'"
